  // 1. Make sure you have Node installed
// 2. Save this file as weather.js
// 3. Run with: node weather.js

// Built-in fetch in newer Node versions
fetch("https://api.open-meteo.com/v1/forecast?latitude=5.56&longitude=-0.20&current_weather=true")
  .then(response => response.json())
  .then(data => {
    console.log("Current Weather in Accra:");
    console.log(data.current_weather);
  })
  .catch(error => {
    console.error("Error fetching weather data:", error);
  });
